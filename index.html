<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="author" content="Lunarbine">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6FootScripts - Home</title>
    <link rel="stylesheet" href="src/styles.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>6F</h1>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="scripts.html">Scripts</a></li>
                <li><a href="contact.html">Contact Us</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-toggle-icon">🌙</span>
                </button>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>6FootScripts welcomes you!</h1>
                <p>Find our scripts for games like AOPG, Pet Simulator 99, Pets Go and more!</p>
                <div class="hero-buttons">
                    <button class="cta-button" onclick="window.location.href='scripts.html'">Explore Scripts</button>
                    <button class="cta-button request-btn" onclick="window.location.href='contact.html#script-request-section'">Request a Script</button>
                </div>
            </div>
        </section>
        <section class="featured-scripts">
            <div class="container">
                <h2>Featured Scripts</h2>
                <p>Discover our most popular and reliable game scripts, trusted by thousands of users worldwide.</p>
                <div class="script-grid">
                    <div class="script-card">
                        <div class="script-header">
                            <h3>AOPG</h3>
                            <span class="status-badge active">✅ Active</span>
                        </div>
                        <p class="script-description">A One Piece Game</p>
                        <div class="script-meta">
                            <span class="last-updated">Last Updated: 2 days ago</span>
                            <span class="recently-updated">Recently Updated</span>
                        </div>
                    </div>
                    <div class="script-card">
                        <div class="script-header">
                            <h3>Pet Simulator 99</h3>
                            <span class="status-badge active">✅ Active</span>
                        </div>
                        <p class="script-description">Pet Simulator 99</p>
                        <div class="script-meta">
                            <span class="last-updated">Last Updated: 1 week ago</span>
                        </div>
                    </div>
                    <div class="script-card">
                        <div class="script-header">
                            <h3>Pets Go</h3>
                            <span class="status-badge maintenance">🔄 Maintenance</span>
                        </div>
                        <p class="script-description">Multi-feature script for Pets Go</p>
                        <div class="script-meta">
                            <span class="last-updated">Last Updated: 3 days ago</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="statistics">
            <div class="container">
                <h2>Our Impact</h2>
                <p>See how we've helped the gaming community with our reliable and efficient scripts.</p>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">12+</div>
                        <div class="stat-label">Total Scripts</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">10+</div>
                        <div class="stat-label">Games Supported</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">1000+</div>
                        <div class="stat-label">Happy Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">Support</div>
                    </div>
                </div>
            </div>
        </section>
        <section class="testimonials">
            <div class="container">
                <h2>What Users Say</h2>
                <p>Real feedback from our community of satisfied users who trust our scripts daily.</p>
                <div class="testimonial-grid">
                    <div class="testimonial-card">
                        <p>"Amazing scripts! Because 6f there duh duh"</p>
                        <div class="testimonial-author">- Sabin07</div>
                    </div>
                    <div class="testimonial-card">
                        <p>"bad script cause tako duh"</p>
                        <div class="testimonial-author">- sabin08</div>
                    </div>
                    <div class="testimonial-card">
                        <p>"Lame support team just because tako"</p>
                        <div class="testimonial-author">- Sabin07</div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 6FootScripts. All rights reserved.</p>
        </div>
    </footer>
    <button class="scroll-to-top" id="scroll-to-top" aria-label="Scroll to top">
        <span>↑</span>
    </button>
    <script src="src/script.js"></script>
</body>
</html>
