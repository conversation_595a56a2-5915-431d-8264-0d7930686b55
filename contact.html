<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="author" content="Lunarbine">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>6FootScripts - Contact Us</title>
    <link rel="stylesheet" href="src/styles.css">
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="logo">
                <h1>6F</h1>
            </div>
            <ul class="nav-menu">
                <li><a href="index.html">Home</a></li>
                <li><a href="scripts.html">Scripts</a></li>
                <li><a href="contact.html">Contact Us</a></li>
            </ul>
            <div class="nav-controls">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle dark mode">
                    <span class="theme-toggle-icon">🌙</span>
                </button>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </nav>
    </header>
    <main>
        <section class="hero">
            <div class="hero-content">
                <h1>Meet Our Team</h1>
                <p>Get in touch with the developers behind 6FootScripts. We're here to help and always open to feedback.</p>
            </div>
        </section>
        <section class="request-script-section" id="script-request-section">
            <div class="container">
                <h2>Request a Script</h2>
                <p>Can't find a script for your favorite game? Request it and we'll consider adding it to our collection!</p>
                <div class="request-form-container">
                    <form class="request-form" id="script-request-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="script-requester-name">Your Name</label>
                                <input type="text" id="script-requester-name" name="requesterName" required minlength="2" maxlength="50">
                            </div>
                            <div class="form-group">
                                <label for="script-requester-discord">Discord Username (Optional)</label>
                                <input type="text" id="script-requester-discord" name="requesterDiscord" placeholder="username#1234" maxlength="50">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="script-game-name">Game Name</label>
                            <input type="text" id="script-game-name" name="gameName" required minlength="2" maxlength="100" placeholder="Enter the exact game name">
                        </div>
                        <div class="form-group">
                            <label for="script-game-link">Game Link</label>
                            <input type="url" id="script-game-link" name="gameLink" required placeholder="https://www.roblox.com/games/..." pattern="https://.*">
                        </div>
                        <div class="form-group">
                            <label for="script-request-priority">Priority Level</label>
                            <select id="script-request-priority" name="priority" required>
                                <option value="">Select priority</option>
                                <option value="low">Low - Just a suggestion</option>
                                <option value="medium">Medium - Would be nice to have</option>
                                <option value="high">High - Really need this script</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="script-features-wanted">Desired Features</label>
                            <textarea id="script-features-wanted" name="featuresWanted" rows="4" required minlength="10" maxlength="500" placeholder="Describe what features you'd like the script to have (auto-farm, GUI, specific functions, etc.)"></textarea>
                            <small class="char-counter">0/500 characters</small>
                        </div>
                        <div class="form-group">
                            <label for="script-additional-info">Additional Information</label>
                            <textarea id="script-additional-info" name="additionalInfo" rows="3" maxlength="300" placeholder="Any other details that might help us (game updates, special requirements, etc.)"></textarea>
                            <small class="char-counter">0/300 characters</small>
                        </div>

                        <!-- Anti-spam measures -->
                        <div class="form-group anti-spam">
                            <label for="script-verification">Verification: What is 5 + 3?</label>
                            <input type="number" id="script-verification" name="verification" required min="8" max="8" placeholder="Enter the answer">
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="script-terms-agree" name="termsAgree" required>
                                <span class="checkmark"></span>
                                I understand that script requests are not guaranteed and development time varies
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="script-no-spam" name="noSpam" required>
                                <span class="checkmark"></span>
                                I agree not to spam requests and understand that duplicate requests may be ignored
                            </label>
                        </div>

                        <button type="submit" class="cta-button request-submit-btn">
                            <span class="btn-text">Submit Request</span>
                            <span class="btn-loading" style="display: none;">Sending...</span>
                        </button>
                    </form>

                    <div class="request-info">
                        <h3>Request Guidelines</h3>
                        <div class="guideline-item">
                            <span class="guideline-icon">⚡</span>
                            <div>
                                <strong>Response Time</strong>
                                <p>We review requests within 24-48 hours</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🎯</span>
                            <div>
                                <strong>Popular Games Priority</strong>
                                <p>Scripts for trending games are prioritized</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🔒</span>
                            <div>
                                <strong>No Guarantees</strong>
                                <p>Not all requests can be fulfilled</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🚫</span>
                            <div>
                                <strong>No Spam Policy</strong>
                                <p>Duplicate or spam requests will be automatically filtered</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="team-section">
            <div class="container">
                <div class="team-grid">
                    <div class="team-card">
                        <div class="team-avatar">
                            <div class="avatar-placeholder">6F4H</div>
                        </div>
                        <div class="team-info">
                            <h3>6Foot4Honda</h3>
                            <div class="team-role">Script Owner & Developer</div>
                            <p class="team-description">Lead developer and owner of 6FootScripts. Responsible for script development, maintenance, and overall project direction.</p>
                            <div class="team-responsibilities">
                                <h4>Responsibilities:</h4>
                                <ul>
                                    <li>Script development and optimization</li>
                                    <li>Project management and planning</li>
                                    <li>Community engagement and support</li>
                                    <li>Quality assurance and testing</li>
                                </ul>
                            </div>
                            <div class="contact-buttons">
                                <a href="https://discord.com/users/6F4H_DISCORD_ID" class="contact-btn discord" target="_blank">
                                    <span class="discord-icon">💬</span>
                                    Contact 6F4H
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="team-card">
                        <div class="team-avatar">
                            <div class="avatar-placeholder">🌙</div>
                        </div>
                        <div class="team-info">
                            <h3>Lunarbine</h3>
                            <div class="team-role">Script Developer & Website Owner</div>
                            <p class="team-description">Script developer and website owner. Handles web development, script coding, and technical infrastructure management.</p>
                            <div class="team-responsibilities">
                                <h4>Responsibilities:</h4>
                                <ul>
                                    <li>Website development and maintenance</li>
                                    <li>Script development and debugging</li>
                                    <li>Server infrastructure management</li>
                                    <li>Technical support and documentation</li>
                                </ul>
                            </div>
                            <div class="contact-buttons">
                                <a href="https://discord.com/users/LUNARBINE_DISCORD_ID" class="contact-btn discord" target="_blank">
                                    <span class="discord-icon">💬</span>
                                    Contact Lunarbine
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <section class="general-contact">
            <div class="container">
                <h2>General Contact Information</h2>
                <div class="contact-info-grid">
                    <div class="contact-info-card">
                        <h3>Discord Server</h3>
                        <p>Join our community for updates, support, and discussions</p>
                        <a href="#" class="contact-btn server">Join Discord Server</a>
                    </div>
                    <div class="contact-info-card">
                        <h3>Support</h3>
                        <p>Need help with our scripts? Contact us directly</p>
                        <div class="support-hours">
                            <strong>Response Time:</strong> Usually within 24 hours
                        </div>
                    </div>
                    <div class="contact-info-card">
                        <h3>Script Requests</h3>
                        <p>Have a game you'd like us to create a script for?</p>
                        <button class="contact-btn request">Request Script</button>
                    </div>
                </div>
            </div>
        </section>
        <section class="faq-section">
            <div class="container">
                <h2>Frequently Asked Questions</h2>
                <div class="faq-grid">
                    <div class="faq-item">
                        <div class="faq-question">How do I use your scripts?</div>
                        <div class="faq-answer">Copy the loadstring code from our Scripts page and paste it into your executor. Make sure you're using a reliable executor for best results.</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">Are your scripts safe to use?</div>
                        <div class="faq-answer">Yes, all our scripts are thoroughly tested and safe to use. We prioritize user safety and script reliability in all our developments.</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">How often do you update scripts?</div>
                        <div class="faq-answer">We update our scripts regularly based on game updates and user feedback. Active scripts are maintained and updated as needed.</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">Can I request a script for a specific game?</div>
                        <div class="faq-answer">Absolutely! Contact us through Discord with your request. We consider all requests and develop scripts based on community interest.</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">What should I do if a script isn't working?</div>
                        <div class="faq-answer">First, check if the script status is "Active" on our Scripts page. If it is and you're still having issues, contact us on Discord with details about the problem.</div>
                    </div>
                    <div class="faq-item">
                        <div class="faq-question">Do you provide script customization?</div>
                        <div class="faq-answer">We focus on creating scripts that work for the general community. For specific customization requests, contact us directly to discuss possibilities.</div>
                    </div>
                </div>
            </div>
        </section>
    </main>
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 6FootScripts. All rights reserved.</p>
        </div>
    </footer>
    <button class="scroll-to-top" id="scroll-to-top" aria-label="Scroll to top">
        <span>↑</span>
    </button>
    <button class="floating-request-btn" id="floating-request-btn" aria-label="Request a script">
        <span class="request-icon">📝</span>
        <span class="request-text">Request Script</span>
    </button>
    <script src="src/script.js"></script>
</body>
</html>
