
window.addEventListener('load', function() {
    const loader = document.querySelector('.page-loader');
    if (loader) {
        setTimeout(() => {
            loader.classList.add('hidden');
            setTimeout(() => loader.remove(), 500);
        }, 500);
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const themeToggle = document.getElementById('theme-toggle');
    const themeIcon = document.querySelector('.theme-toggle-icon');

    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    updateThemeIcon(savedTheme);

    function updateThemeIcon(theme) {
        if (themeIcon) {
            themeIcon.textContent = theme === 'dark' ? '☀️' : '🌙';
        }
    }

    function toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';

        document.documentElement.setAttribute('data-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        updateThemeIcon(newTheme);
    }

    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    const contactForm = document.getElementById('contact-form');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(contactForm);
            const data = Object.fromEntries(formData);

            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Sending...';
            submitBtn.disabled = true;
            submitBtn.classList.add('loading');

            setTimeout(() => {
                submitBtn.textContent = 'Message Sent! ✓';
                submitBtn.style.background = 'var(--secondary-color)';

                setTimeout(() => {
                    contactForm.reset();
                    submitBtn.textContent = originalText;
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('loading');
                    submitBtn.style.background = '';
                }, 2000);
            }, 1500);
        });
    }


    function enhancedCopyToClipboard(text, button) {
        navigator.clipboard.writeText(text).then(() => {
            button.classList.add('copying');

            showToast('Script copied to clipboard!');

            setTimeout(() => {
                button.classList.remove('copying');
            }, 1000);
        }).catch(err => {
            console.error('Failed to copy: ', err);
            showToast('Failed to copy script', 'error');
        });
    }

    function showToast(message, type = 'success') {
        const existingToast = document.querySelector('.toast');
        if (existingToast) {
            existingToast.remove();
        }

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => toast.classList.add('show'), 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    const scrollToTopBtn = document.getElementById('scroll-to-top');
    const floatingRequestBtn = document.getElementById('floating-request-btn');

    if (scrollToTopBtn) {
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                scrollToTopBtn.classList.add('visible');
            } else {
                scrollToTopBtn.classList.remove('visible');
            }

            // Show floating request button after user scrolls past hero
            if (floatingRequestBtn) {
                const heroSection = document.querySelector('.hero');
                if (heroSection) {
                    const heroBottom = heroSection.offsetTop + heroSection.offsetHeight;
                    if (window.pageYOffset > heroBottom) {
                        floatingRequestBtn.classList.add('visible');
                    } else {
                        floatingRequestBtn.classList.remove('visible');
                    }
                }
            }
        });

        scrollToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    // Floating request button click handler
    if (floatingRequestBtn) {
        floatingRequestBtn.addEventListener('click', function() {
            // If we're on contact page, scroll to section, otherwise navigate
            if (window.location.pathname.includes('contact.html')) {
                scrollToSection('script-request-section');
            } else {
                window.location.href = 'contact.html#script-request-section';
            }
        });
    }

    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && navMenu && navMenu.classList.contains('active')) {
            navMenu.classList.remove('active');
            hamburger.classList.remove('active');
        }

        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
            e.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }
        if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
            e.preventDefault();
            toggleTheme();
        }
    });

    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    const filterButtons = document.querySelectorAll('.filter-btn');
    const scriptCards = document.querySelectorAll('.script-card-detailed');
    const searchInput = document.querySelector('.search-input');
    const copyButtons = document.querySelectorAll('.copy-btn');
    const faqItems = document.querySelectorAll('.faq-item');

    if (hamburger && navMenu) {
        hamburger.addEventListener('click', function() {
            navMenu.classList.toggle('active');
            hamburger.classList.toggle('active');
        });

        document.addEventListener('click', function(e) {
            if (!hamburger.contains(e.target) && !navMenu.contains(e.target)) {
                navMenu.classList.remove('active');
                hamburger.classList.remove('active');
            }
        });
    }

    if (filterButtons.length > 0 && scriptCards.length > 0) {
        filterButtons.forEach(button => {
            button.addEventListener('click', function() {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filter = this.getAttribute('data-filter');
                
                scriptCards.forEach(card => {
                    if (filter === 'all') {
                        card.style.display = 'block';
                        setTimeout(() => card.classList.remove('hidden'), 10);
                    } else {
                        const category = card.getAttribute('data-category');
                        if (category === filter) {
                            card.style.display = 'block';
                            setTimeout(() => card.classList.remove('hidden'), 10);
                        } else {
                            card.classList.add('hidden');
                            setTimeout(() => card.style.display = 'none', 300);
                        }
                    }
                });
            });
        });
    }

    if (searchInput && scriptCards.length > 0) {
        searchInput.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            
            scriptCards.forEach(card => {
                const gameName = card.getAttribute('data-game').toLowerCase();
                const scriptTitle = card.querySelector('h3').textContent.toLowerCase();
                const description = card.querySelector('.script-description').textContent.toLowerCase();
                
                if (gameName.includes(searchTerm) || 
                    scriptTitle.includes(searchTerm) || 
                    description.includes(searchTerm)) {
                    card.style.display = 'block';
                    card.classList.remove('hidden');
                } else {
                    card.classList.add('hidden');
                    setTimeout(() => card.style.display = 'none', 300);
                }
            });
        });
    }

    copyButtons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.classList.contains('disabled')) return;

            const code = this.getAttribute('data-code');
            if (code) {
                enhancedCopyToClipboard(code, this);
            }
        });
    });

    faqItems.forEach(item => {
        const question = item.querySelector('.faq-question');
        if (question) {
            question.addEventListener('click', function() {
                const isActive = item.classList.contains('active');
                
                faqItems.forEach(faq => faq.classList.remove('active'));
                
                if (!isActive) {
                    item.classList.add('active');
                }
            });
        }
    });

    const ctaButton = document.querySelector('.cta-button');
    const requestButtons = document.querySelectorAll('.contact-btn.request');
    
    if (ctaButton) {
        ctaButton.addEventListener('click', function() {
            window.location.href = 'contact.html';
        });
    }
    
    requestButtons.forEach(button => {
        button.addEventListener('click', function() {
            alert('Please contact us on Discord to request a script!');
        });
    });

    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    const animateElements = document.querySelectorAll('.script-card, .stat-card, .testimonial-card, .team-card, .faq-item');
    animateElements.forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });

    const currentPage = window.location.pathname.split('/').pop() || 'index.html';
    const navLinks = document.querySelectorAll('.nav-menu a');
    
    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPage || (currentPage === '' && href === 'index.html')) {
            link.classList.add('active');
        }
    });

    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            if (searchInput) {
                searchInput.focus();
            }
        }
    });

    const smoothScrollLinks = document.querySelectorAll('a[href^="#"]');
    smoothScrollLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    let lastScrollTop = 0;
    const navbar = document.querySelector('.navbar');
    
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        
        if (scrollTop > lastScrollTop && scrollTop > 100) {
            navbar.style.transform = 'translateY(-100%)';
        } else {
            navbar.style.transform = 'translateY(0)';
        }
        
        lastScrollTop = scrollTop;
    });

    const parallaxElements = document.querySelectorAll('.hero, .scripts-hero, .contact-hero');
    
    window.addEventListener('scroll', function() {
        const scrolled = window.pageYOffset;
        
        parallaxElements.forEach(element => {
            const rate = scrolled * -0.5;
            element.style.transform = `translateY(${rate}px)`;
        });
    });

    const loadingStates = new Map();

    const scriptCardsDetailed = document.querySelectorAll('.script-card-detailed');
    scriptCardsDetailed.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
    });

    const statNumbers = document.querySelectorAll('.stat-number');
    const statsObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = entry.target;
                const finalNumber = target.textContent;
                const isPlus = finalNumber.includes('+');
                const numericValue = parseInt(finalNumber.replace(/\D/g, ''));
                
                if (!isNaN(numericValue)) {
                    animateNumber(target, 0, numericValue, isPlus);
                }
                
                statsObserver.unobserve(target);
            }
        });
    }, { threshold: 0.5 });

    statNumbers.forEach(stat => {
        statsObserver.observe(stat);
    });

    function animateNumber(element, start, end, hasPlus) {
        const duration = 2000;
        const startTime = performance.now();
        
        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(start + (end - start) * easeOutQuart(progress));
            element.textContent = hasPlus ? `${current}+` : current;
            
            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }
        
        requestAnimationFrame(update);
    }

    function easeOutQuart(t) {
        return 1 - Math.pow(1 - t, 4);
    }

    const typewriterElements = document.querySelectorAll('.hero h1, .scripts-hero h1, .contact-hero h1');
    
    function typeWriter(element, text, speed = 100) {
        element.textContent = '';
        let i = 0;
        
        function type() {
            if (i < text.length) {
                element.textContent += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    const heroObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const heading = entry.target;
                const originalText = heading.textContent;
                typeWriter(heading, originalText, 80);
                heroObserver.unobserve(heading);
            }
        });
    }, { threshold: 0.5 });

    typewriterElements.forEach(element => {
        heroObserver.observe(element);
    });

    const glowElements = document.querySelectorAll('.cta-button, .copy-btn, .contact-btn');
    
    glowElements.forEach(element => {
        element.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 0 30px rgba(102, 126, 234, 0.8)';
        });
        
        element.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
    });

    const cards = document.querySelectorAll('.script-card, .script-card-detailed, .team-card, .stat-card, .testimonial-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
    });



    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    if (prefersReducedMotion.matches) {
        document.documentElement.style.setProperty('--animation-duration', '0.01ms');
        document.documentElement.style.setProperty('--transition-duration', '0.01ms');
    }

    const lazyImages = document.querySelectorAll('img[data-src]');
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });

    lazyImages.forEach(img => {
        imageObserver.observe(img);
    });

    window.addEventListener('beforeunload', function() {
        document.body.style.opacity = '0';
    });

    const focusableElements = document.querySelectorAll('button, a, input, [tabindex]');
    
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.style.outline = '2px solid rgba(102, 126, 234, 0.8)';
            this.style.outlineOffset = '2px';
        });
        
        element.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.outlineOffset = '';
        });
    });

    const tooltips = document.querySelectorAll('[data-tooltip]');
    
    tooltips.forEach(element => {
        element.addEventListener('mouseenter', function() {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = this.getAttribute('data-tooltip');
            tooltip.style.cssText = `
                position: absolute;
                background: var(--primary-gradient);
                color: white;
                padding: 0.5rem 1rem;
                border-radius: 5px;
                font-size: 0.9rem;
                z-index: 1000;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            
            document.body.appendChild(tooltip);
            
            const rect = this.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 10 + 'px';
            
            setTimeout(() => tooltip.style.opacity = '1', 10);
            
            this.tooltipElement = tooltip;
        });
        
        element.addEventListener('mouseleave', function() {
            if (this.tooltipElement) {
                this.tooltipElement.style.opacity = '0';
                setTimeout(() => {
                    if (this.tooltipElement && this.tooltipElement.parentNode) {
                        this.tooltipElement.parentNode.removeChild(this.tooltipElement);
                    }
                }, 300);
            }
        });
    });

    // Script Request Form functionality
    const requestForm = document.getElementById('script-request-form');
    console.log('🔍 Looking for form with ID: script-request-form');
    console.log('🔍 Form found:', requestForm);
    console.log('🔍 Current page:', window.location.pathname);
    console.log('🔍 All forms on page:', document.querySelectorAll('form'));
    if (requestForm) {
        console.log('✅ Form detected, setting up event listener');
        // Character counters
        const textareas = requestForm.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            const counter = textarea.parentNode.querySelector('.char-counter');
            if (counter) {
                const maxLength = textarea.getAttribute('maxlength');

                function updateCounter() {
                    const currentLength = textarea.value.length;
                    counter.textContent = `${currentLength}/${maxLength} characters`;

                    if (currentLength > maxLength * 0.9) {
                        counter.style.color = 'var(--accent-color)';
                    } else {
                        counter.style.color = 'var(--text-light)';
                    }
                }

                textarea.addEventListener('input', updateCounter);
                updateCounter(); // Initial count
            }
        });

        // Rate limiting (prevent spam)
        const RATE_LIMIT_KEY = 'lastRequestTime';
        const RATE_LIMIT_DURATION = 5 * 60 * 1000; // 5 minutes

        function checkRateLimit() {
            const lastRequestTime = localStorage.getItem(RATE_LIMIT_KEY);
            if (lastRequestTime) {
                const timeSinceLastRequest = Date.now() - parseInt(lastRequestTime);
                if (timeSinceLastRequest < RATE_LIMIT_DURATION) {
                    const remainingTime = Math.ceil((RATE_LIMIT_DURATION - timeSinceLastRequest) / 1000 / 60);
                    return remainingTime;
                }
            }
            return 0;
        }

        function showRateLimitWarning(minutes) {
            const warning = document.createElement('div');
            warning.className = 'rate-limit-warning';
            warning.innerHTML = `⚠️ Please wait ${minutes} more minute(s) before submitting another request to prevent spam.`;
            requestForm.insertBefore(warning, requestForm.firstChild);

            setTimeout(() => warning.remove(), 5000);
        }

        console.log('🔧 Setting up form submission handling');

        // Handle form submission - SIMPLE AND DIRECT
        async function handleFormSubmit() {
            console.log('🚀 FORM SUBMIT TRIGGERED!');

            // Get form data
            const formData = new FormData(requestForm);
            const data = Object.fromEntries(formData);
            console.log('📋 Form data:', data);

            // Simple validation
            if (!data.requesterName || !data.gameName || !data.gameLink || !data.priority || !data.featuresWanted || data.verification !== '8') {
                showToast('Please fill all required fields correctly', 'error');
                return;
            }

            if (!data.termsAgree || !data.noSpam) {
                showToast('Please agree to all terms', 'error');
                return;
            }

            // Show loading
            const submitBtn = requestForm.querySelector('.request-submit-btn');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            btnText.style.display = 'none';
            btnLoading.style.display = 'block';
            submitBtn.disabled = true;

            try {
                // Send webhook
                const response = await fetch('https://discord.com/api/webhooks/1360446266638602383/5XJxwLWnobc1rz-zcZZ6H9ZFJKa0Pk0GkY3s66HfxbsduxezFeeT-wLJ7W0J4GVbn0Jy', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        content: `🎮 **New Script Request**\n**Requester:** ${data.requesterName}\n**Game:** ${data.gameName}\n**Priority:** ${data.priority}\n**Features:** ${data.featuresWanted}`
                    })
                });

                if (response.ok) {
                    showToast('Request submitted successfully!', 'success');
                    requestForm.reset();
                } else {
                    throw new Error('Webhook failed');
                }
            } catch (error) {
                console.error('Webhook error:', error);
                showToast('Failed to submit. Please try again.', 'error');
            } finally {
                btnText.style.display = 'block';
                btnLoading.style.display = 'none';
                submitBtn.disabled = false;
            }
        }

        // Add BOTH event listeners
        requestForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleFormSubmit();
        });

        const submitBtn = requestForm.querySelector('.request-submit-btn');
        if (submitBtn) {
            submitBtn.addEventListener('click', function(e) {
                e.preventDefault();
                handleFormSubmit();
            });
        }
    } else {
        console.error('❌ Form not found! Available forms:', document.querySelectorAll('form'));
    }
});

function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}



// Test function to verify webhook is working
window.testWebhook = async function() {
    console.log('🧪 Testing webhook...');
    try {
        const response = await fetch('https://discord.com/api/webhooks/1360446266638602383/5XJxwLWnobc1rz-zcZZ6H9ZFJKa0Pk0GkY3s66HfxbsduxezFeeT-wLJ7W0J4GVbn0Jy', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                content: '🧪 Test message from website - ' + new Date().toLocaleTimeString()
            })
        });

        console.log('Test response status:', response.status);
        console.log('Test response ok:', response.ok);

        if (response.ok) {
            console.log('✅ Webhook test successful!');
            return true;
        } else {
            const errorText = await response.text();
            console.log('❌ Webhook test failed:', errorText);
            return false;
        }
    } catch (error) {
        console.error('❌ Webhook test error:', error);
        return false;
    }
};

// Test function for form submission
window.testFormSubmission = function() {
    const form = document.getElementById('script-request-form');
    if (!form) {
        console.error('❌ Form not found!');
        return;
    }

    console.log('🧪 Testing form submission...');

    // Fill form with test data
    form.querySelector('[name="requesterName"]').value = 'Test User';
    form.querySelector('[name="gameName"]').value = 'Test Game';
    form.querySelector('[name="gameLink"]').value = 'https://www.roblox.com/games/test';
    form.querySelector('[name="priority"]').value = 'medium';
    form.querySelector('[name="featuresWanted"]').value = 'Test features for webhook testing';
    form.querySelector('[name="verification"]').value = '8';
    form.querySelector('[name="termsAgree"]').checked = true;
    form.querySelector('[name="noSpam"]').checked = true;

    // Submit the form
    form.dispatchEvent(new Event('submit', { bubbles: true, cancelable: true }));
};

// Debug function to check current form state
window.debugFormState = function() {
    const form = document.getElementById('script-request-form');
    if (!form) {
        console.error('❌ Form not found!');
        return;
    }

    console.log('🔍 Current form state:');
    console.log('Form valid:', form.checkValidity());
    console.log('Form data:', Object.fromEntries(new FormData(form)));

    // Check each required field
    const requiredFields = form.querySelectorAll('[required]');
    requiredFields.forEach(field => {
        console.log(`Field ${field.name}:`, {
            value: field.value,
            valid: field.checkValidity(),
            validationMessage: field.validationMessage
        });
    });

    // Check checkboxes
    const checkboxes = form.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        console.log(`Checkbox ${checkbox.name}:`, {
            checked: checkbox.checked,
            required: checkbox.required
        });
    });
};
