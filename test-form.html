<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Form - 6FootScripts</title>
    <link rel="stylesheet" href="src/style.css">
</head>
<body>
    <div class="page-loader">
        <div class="loader-content">
            <div class="loader-spinner"></div>
            <p>Loading 6FootScripts...</p>
        </div>
    </div>

    <header class="navbar">
        <div class="container">
            <div class="nav-brand">
                <a href="index.html">6FootScripts</a>
            </div>
            <nav class="nav-menu">
                <a href="index.html">Home</a>
                <a href="scripts.html">Scripts</a>
                <a href="contact.html">Contact</a>
            </nav>
            <div class="nav-actions">
                <button class="theme-toggle" id="theme-toggle" aria-label="Toggle theme">
                    <span class="theme-toggle-icon">🌙</span>
                </button>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    </header>

    <main class="main-content">
        <section class="contact-hero">
            <div class="container">
                <h1>TEST FORM - PRE-FILLED</h1>
                <p>Just click submit to test the webhook!</p>
            </div>
        </section>

        <section class="request-script-section" id="script-request-section">
            <div class="container">
                <h2>Test Script Request Form</h2>
                <p>All fields are pre-filled. Just click Submit Request to test!</p>
                <div class="request-form-container">
                    <form class="request-form" id="script-request-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="script-requester-name">Your Name</label>
                                <input type="text" id="script-requester-name" name="requesterName" required minlength="2" maxlength="50" value="Test User">
                            </div>
                            <div class="form-group">
                                <label for="script-requester-discord">Discord Username (Optional)</label>
                                <input type="text" id="script-requester-discord" name="requesterDiscord" placeholder="username#1234" maxlength="50" value="testuser#1234">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="script-game-name">Game Name</label>
                            <input type="text" id="script-game-name" name="gameName" required minlength="2" maxlength="100" placeholder="Enter the exact game name" value="Test Game for Webhook">
                        </div>
                        <div class="form-group">
                            <label for="script-game-link">Game Link</label>
                            <input type="url" id="script-game-link" name="gameLink" required placeholder="https://www.roblox.com/games/..." pattern="https://.*" value="https://www.roblox.com/games/123456789/test-game">
                        </div>
                        <div class="form-group">
                            <label for="script-request-priority">Priority Level</label>
                            <select id="script-request-priority" name="priority" required>
                                <option value="">Select priority</option>
                                <option value="low">Low - Just a suggestion</option>
                                <option value="medium" selected>Medium - Would be nice to have</option>
                                <option value="high">High - Really need this script</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="script-features-wanted">Desired Features</label>
                            <textarea id="script-features-wanted" name="featuresWanted" rows="4" required minlength="10" maxlength="500" placeholder="Describe what features you'd like the script to have">Auto-farm script with GUI, teleport features, and auto-collect items. Should work with the latest game updates and have customizable settings.</textarea>
                            <small class="char-counter">0/500 characters</small>
                        </div>
                        <div class="form-group">
                            <label for="script-additional-info">Additional Information</label>
                            <textarea id="script-additional-info" name="additionalInfo" rows="3" maxlength="300" placeholder="Any other details that might help us">This is a test submission to verify the webhook is working correctly. Please ignore this request.</textarea>
                            <small class="char-counter">0/300 characters</small>
                        </div>

                        <!-- Anti-spam measures -->
                        <div class="form-group anti-spam">
                            <label for="script-verification">Verification: What is 5 + 3?</label>
                            <input type="number" id="script-verification" name="verification" required min="8" max="8" placeholder="Enter the answer" value="8">
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="script-terms-agree" name="termsAgree" required checked>
                                <span class="checkmark"></span>
                                I understand that script requests are not guaranteed and development time varies
                            </label>
                        </div>

                        <div class="form-group checkbox-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="script-no-spam" name="noSpam" required checked>
                                <span class="checkmark"></span>
                                I agree not to spam requests and understand that duplicate requests may be ignored
                            </label>
                        </div>

                        <button type="submit" class="cta-button request-submit-btn">
                            <span class="btn-text">Submit Request</span>
                            <span class="btn-loading" style="display: none;">Sending...</span>
                        </button>
                    </form>

                    <div class="request-info">
                        <h3>Test Instructions</h3>
                        <div class="guideline-item">
                            <span class="guideline-icon">🧪</span>
                            <div>
                                <strong>Pre-filled Form</strong>
                                <p>All fields are already filled with test data</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">🚀</span>
                            <div>
                                <strong>Just Click Submit</strong>
                                <p>Click the Submit Request button to test the webhook</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">📝</span>
                            <div>
                                <strong>Check Console</strong>
                                <p>Open browser console to see detailed logs</p>
                            </div>
                        </div>
                        <div class="guideline-item">
                            <span class="guideline-icon">💬</span>
                            <div>
                                <strong>Check Discord</strong>
                                <p>Verify the message appears in your Discord channel</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 6FootScripts Test Page. All rights reserved.</p>
        </div>
    </footer>

    <button class="scroll-to-top" id="scroll-to-top" aria-label="Scroll to top">
        <span>↑</span>
    </button>

    <script src="src/script.js"></script>
</body>
</html>
